#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动数据库清理脚本
用于测试和手动执行清理任务
"""

import sys
import os
from db_cleaner import DatabaseCleaner, TableConfig
from config import DB_CONFIG, BACKUP_PATH, TABLE_CONFIGS, SCP_CONFIG


def manual_cleanup(table_name: str = None):
    """
    手动执行清理任务
    
    Args:
        table_name: 指定要清理的表名，如果为None则清理所有配置的表
    """
    # 创建清理器
    cleaner = DatabaseCleaner(DB_CONFIG, BACKUP_PATH)
    
    # 获取要清理的表配置
    if table_name:
        # 查找指定表的配置
        target_configs = [config for config in TABLE_CONFIGS if config.table_name == table_name]
        if not target_configs:
            cleaner.logger.error(f"未找到表 {table_name} 的配置")
            return False
    else:
        # 清理所有配置的表
        target_configs = TABLE_CONFIGS
    
    success_count = 0
    total_count = len(target_configs)
    
    for config in target_configs:
        cleaner.logger.info(f"开始手动清理表: {config.table_name}")
        success = cleaner.clean_table(config)
        if success:
            success_count += 1
            cleaner.logger.info(f"手动清理完成: {config.table_name}")
        else:
            cleaner.logger.error(f"手动清理失败: {config.table_name}")
    
    cleaner.logger.info(f"手动清理任务完成: 成功 {success_count}/{total_count}")
    return success_count == total_count


def list_tables():
    """列出所有配置的表"""
    print("配置的表清理任务:")
    print("-" * 120)
    print(f"{'表名':<20} {'主键':<8} {'每批数量':<10} {'总数量':<10} {'定时时间':<10} {'时间字段':<12} {'保留天数':<8}")
    print("-" * 120)

    for config in TABLE_CONFIGS:
        print(f"{config.table_name:<20} {config.primary_key:<8} {config.cleanup_count:<10} "
              f"{config.total_cleanup_count:<10} {config.schedule_time:<10} "
              f"{config.time_field:<12} {config.min_keep_days:<8}")


def test_scp():
    """测试SCP连接和同步功能"""
    import os
    import csv
    from datetime import datetime

    print("测试SCP远程备份功能...")
    print(f"SCP配置: 主机={SCP_CONFIG.get('host')}, 用户={SCP_CONFIG.get('username')}, 远程路径={SCP_CONFIG.get('remote_path')}")

    if not SCP_CONFIG.get('enabled', False):
        print("❌ SCP功能未启用")
        return False

    # 创建测试表目录和文件
    test_table = 'test_table'
    test_dir = os.path.join(BACKUP_PATH, test_table)
    os.makedirs(test_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_file = os.path.join(test_dir, f"{test_table}_1-100_{timestamp}.csv")

    # 写入测试数据
    test_data = [
        {'id': 1, 'name': 'SCP测试数据1', 'created_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
        {'id': 2, 'name': 'SCP测试数据2', 'created_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
    ]

    with open(test_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = test_data[0].keys()
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(test_data)

    print(f"测试文件已创建: {test_file}")

    # 创建清理器并测试同步
    cleaner = DatabaseCleaner(DB_CONFIG, BACKUP_PATH, SCP_CONFIG)

    # 测试获取未同步文件
    unsynced_files = cleaner._get_unsynced_files(test_table)
    print(f"发现未同步文件: {len(unsynced_files)} 个")

    # 测试同步功能
    success = cleaner._sync_table_backups(test_table)

    if success:
        print("✅ SCP同步测试成功！")
        print(f"文件已上传到: {SCP_CONFIG.get('remote_path')}/{test_table}/")

        # 检查文件是否被标记为已同步
        synced_file = test_file + '.syn'
        if os.path.exists(synced_file):
            print(f"✅ 文件已标记为已同步: {os.path.basename(synced_file)}")
        else:
            print("⚠️ 文件未被正确标记为已同步")
    else:
        print("❌ SCP同步测试失败！")

    return success


def sync_table_files(table_name: str = None):
    """
    手动同步表的备份文件

    Args:
        table_name: 指定要同步的表名，如果为None则同步所有表
    """
    # 创建清理器
    cleaner = DatabaseCleaner(DB_CONFIG, BACKUP_PATH, SCP_CONFIG)

    if table_name:
        # 同步指定表
        print(f"开始同步表 {table_name} 的备份文件...")
        success = cleaner._sync_table_backups(table_name)
        if success:
            print(f"表 {table_name} 文件同步完成!")
        else:
            print(f"表 {table_name} 文件同步失败，请查看日志!")
        return success
    else:
        # 同步所有表
        print("开始同步所有表的备份文件...")
        success_count = 0
        total_count = 0

        # 遍历备份目录下的所有表目录
        if os.path.exists(BACKUP_PATH):
            for item in os.listdir(BACKUP_PATH):
                item_path = os.path.join(BACKUP_PATH, item)
                if os.path.isdir(item_path):
                    total_count += 1
                    print(f"正在同步表: {item}")
                    if cleaner._sync_table_backups(item):
                        success_count += 1
                        print(f"表 {item} 同步完成")
                    else:
                        print(f"表 {item} 同步失败")

        print(f"所有表同步完成: 成功 {success_count}/{total_count}")
        return success_count == total_count


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python manual_cleanup.py list                    # 列出所有配置的表")
        print("  python manual_cleanup.py all                     # 清理所有配置的表")
        print("  python manual_cleanup.py test-scp                # 测试SCP远程备份功能")
        print("  python manual_cleanup.py sync-all                # 同步所有表的备份文件")
        print("  python manual_cleanup.py sync <table_name>       # 同步指定表的备份文件")
        print("  python manual_cleanup.py <table_name>            # 清理指定表")
        print("")
        print("示例:")
        print("  python manual_cleanup.py list")
        print("  python manual_cleanup.py all")
        print("  python manual_cleanup.py test-scp")
        print("  python manual_cleanup.py sync-all")
        print("  python manual_cleanup.py sync user_logs")
        print("  python manual_cleanup.py user_logs")
        return
    
    command = sys.argv[1]
    
    if command == 'list':
        list_tables()
    elif command == 'all':
        print("开始清理所有配置的表...")
        success = manual_cleanup()
        if success:
            print("所有表清理完成!")
        else:
            print("部分表清理失败，请查看日志!")
    elif command == 'test-scp':
        print("开始测试SCP功能...")
        success = test_scp()
        if success:
            print("SCP功能测试通过!")
        else:
            print("SCP功能测试失败，请检查配置和网络连接!")
    elif command == 'sync-all':
        print("开始同步所有表的备份文件...")
        success = sync_table_files()
        if success:
            print("所有表文件同步完成!")
        else:
            print("部分表文件同步失败，请查看日志!")
    elif command == 'sync':
        if len(sys.argv) < 3:
            print("错误: 请指定要同步的表名")
            print("使用方法: python manual_cleanup.py sync <table_name>")
            return
        table_name = sys.argv[2]
        print(f"开始同步表 {table_name} 的备份文件...")
        success = sync_table_files(table_name)
        if success:
            print(f"表 {table_name} 文件同步完成!")
        else:
            print(f"表 {table_name} 文件同步失败，请查看日志!")
    else:
        # 清理指定表
        table_name = command
        print(f"开始清理表: {table_name}")
        success = manual_cleanup(table_name)
        if success:
            print(f"表 {table_name} 清理完成!")
        else:
            print(f"表 {table_name} 清理失败，请查看日志!")


if __name__ == '__main__':
    main()
