#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理配置文件
"""

from dataclasses import dataclass
from typing import List


@dataclass
class TableConfig:
    """表清理配置"""
    table_name: str
    primary_key: str = 'id'
    cleanup_count: int = 100000  # 每次清理的记录数
    total_cleanup_count: int = 1000000  # 总共需要清理的记录数
    schedule_time: str = '02:00'  # 清理时间 HH:MM 格式
    time_field: str = 'created_at'  # 时间字段名
    min_keep_days: int = 360  # 保留最近N天的数据


# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '7j03a^6xQ&w8FL@=1b',
    'database': 'hds',
    'charset': 'utf8mb4'
}

# 备份路径配置
BACKUP_PATH = './backups'

# SCP 远程备份配置
SCP_CONFIG = {
    'enabled': True,                           # 是否启用远程备份
    'host': '************',                   # 远程主机IP
    'port': 22,                               # SSH端口
    'username': 'M088607',                    # 用户名
    'password': '20250520a',                  # 密码
    'remote_path': '/share/data-backup',      # 远程目录路径
    'timeout': 300,                           # 传输超时时间（秒）
    'retry_count': 3,                         # 重试次数
    'delete_local_after_upload': False        # 上传后是否删除本地文件
}

# 表清理配置
# 请根据实际需要修改以下配置
TABLE_CONFIGS = [
    # 示例配置，请根据实际表名和需求修改
    TableConfig(
        table_name='t_biz_ics_log',           # 表名
        primary_key='id',                     # 主键字段名
        cleanup_count=100000,                 # 每次清理X万条
        total_cleanup_count=1000000,          # 总共清理X万条
        schedule_time='18:30',                # 执行时间
        time_field='log_time',              # 时间字段名
        min_keep_days=360                      # 保留最近X天数据
    ),
    TableConfig(
            table_name='t_biz_kv8000_data',           # 表名
            primary_key='id',                     # 主键字段名
            cleanup_count=100000,                 # 每次清理X万条
            total_cleanup_count=100000,          # 总共清理X万条
            schedule_time='10:33',                # 执行时间
            time_field='read_time',              # 时间字段名
            min_keep_days=360                      # 保留最近X天数据
     ),
    # 添加更多表配置...
    # TableConfig(
    #     table_name='your_table_name',
    #     primary_key='id',
    #     cleanup_count=100000,
    #     total_cleanup_count=1000000,
    #     schedule_time='04:00',
    #     time_field='created_at',
    #     min_keep_days=30
    # ),
]
