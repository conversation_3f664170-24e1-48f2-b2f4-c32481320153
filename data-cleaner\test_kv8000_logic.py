#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试t_biz_kv8000_data表的特殊清理逻辑
"""

from db_cleaner import DatabaseCleaner
from config import DB_CONFIG

def test_kv8000_cleanup_logic():
    """测试t_biz_kv8000_data表的特殊清理逻辑"""
    
    print("🧪 测试t_biz_kv8000_data表的特殊清理逻辑...")
    
    cleaner = DatabaseCleaner(DB_CONFIG)
    
    try:
        print("📊 测试t_biz_kv8000_data表的数据获取...")
        
        # 测试t_biz_kv8000_data表（使用特殊逻辑）
        data, min_id, max_id = cleaner._get_cleanup_data(
            table_name='t_biz_kv8000_data',
            primary_key='id', 
            cleanup_count=10,  # 只获取10条数据进行测试
            time_field='read_time',
            min_keep_days=360
        )
        
        print(f"✅ t_biz_kv8000_data表查询成功！")
        print(f"   获取数据条数: {len(data)}")
        if data:
            print(f"   ID范围: {min_id} - {max_id}")
            print(f"   时间范围: {data[0]['read_time']} - {data[-1]['read_time']}")
        
        print("\n" + "="*50)
        
        # 测试其他表（使用原有逻辑）
        print("📊 测试其他表的数据获取（原有逻辑）...")
        
        data2, min_id2, max_id2 = cleaner._get_cleanup_data(
            table_name='t_biz_ics_log',
            primary_key='id', 
            cleanup_count=5,
            time_field='log_time',
            min_keep_days=360
        )
        
        print(f"✅ t_biz_ics_log表查询成功！")
        print(f"   获取数据条数: {len(data2)}")
        if data2:
            print(f"   ID范围: {min_id2} - {max_id2}")
        
        print("\n💡 t_biz_kv8000_data表使用了特殊的查询和过滤逻辑")
        print("💡 其他表保持原有的查询逻辑")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_kv8000_cleanup_logic()
