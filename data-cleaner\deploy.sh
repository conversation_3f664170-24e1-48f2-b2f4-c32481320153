#!/bin/bash

# 数据库清理服务部署脚本

set -e

# 配置
IMAGE_NAME="db-cleaner"
CONTAINER_NAME="db-cleaner"

echo "🚀 数据库清理服务部署"
echo "===================="

# 检查Podman
if ! command -v podman &> /dev/null; then
    echo "❌ 未找到 Podman"
    exit 1
fi

# 检查基础镜像
if ! podman image exists "localhost/python-base:latest"; then
    echo "❌ 基础镜像不存在: localhost/python-base:latest"
    exit 1
fi

# 构建应用镜像
echo "🔨 构建应用镜像..."
podman build -t "$IMAGE_NAME" .

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

# 停止旧容器
echo "🛑 停止旧容器..."
podman stop "$CONTAINER_NAME" 2>/dev/null || true
podman rm "$CONTAINER_NAME" 2>/dev/null || true

# 获取当前目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 创建目录
echo "📁 创建目录..."
echo "   脚本目录: $SCRIPT_DIR"
mkdir -p "$SCRIPT_DIR/backups" "$SCRIPT_DIR/logs"
echo "   备份目录: $SCRIPT_DIR/backups"
echo "   日志目录: $SCRIPT_DIR/logs"

# 启动新容器
echo "🚀 启动容器..."
podman run -d \
    --name "$CONTAINER_NAME" \
    --restart unless-stopped \
    -v "$SCRIPT_DIR/backups:/app/backups:Z" \
    -v "$SCRIPT_DIR/logs:/app/logs:Z" \
    -e TZ=Asia/Shanghai \
    "$IMAGE_NAME"

if [ $? -eq 0 ]; then
    echo "✅ 部署成功！"
    echo ""
    
    # 验证目录挂载
    echo "🔍 验证目录挂载..."
    echo "   主机备份目录: $(ls -la "$SCRIPT_DIR/backups" 2>/dev/null || echo "目录不存在")"
    echo "   主机日志目录: $(ls -la "$SCRIPT_DIR/logs" 2>/dev/null || echo "目录不存在")"
    
    # 等待容器启动
    sleep 2
    
    # 检查容器内目录
    echo "   容器备份目录: $(podman exec $CONTAINER_NAME ls -la /app/backups 2>/dev/null || echo "无法访问")"
    echo "   容器日志目录: $(podman exec $CONTAINER_NAME ls -la /app/logs 2>/dev/null || echo "无法访问")"
    
    echo ""
    echo "📋 管理命令:"
    echo "  查看状态: podman ps --filter name=$CONTAINER_NAME"
    echo "  查看日志: podman logs -f $CONTAINER_NAME"
    echo "  进入容器: podman exec -it $CONTAINER_NAME /bin/bash"
    echo "  手动清理: podman exec $CONTAINER_NAME python manual_cleanup.py list"
    echo "  停止服务: podman stop $CONTAINER_NAME"
    echo "  查看挂载: podman exec $CONTAINER_NAME df -h"
else
    echo "❌ 启动失败"
    exit 1
fi
