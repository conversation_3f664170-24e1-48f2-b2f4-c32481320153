# 数据库清理服务

自动化数据库历史数据清理服务，支持定时清理、数据备份、智能远程同步等功能。

## 功能特性

- 🕒 **定时清理**: 支持按时间表自动执行清理任务
- 💾 **数据备份**: 清理前自动备份数据为CSV格式
- 📤 **智能同步**: 按表管理，自动同步未上传的备份文件
- 🏷️ **同步标记**: 成功上传的文件自动添加 .syn 后缀
- 🔧 **灵活配置**: 支持多表配置，每个表独立设置清理参数
- 📊 **批量处理**: 大数据量分批处理，避免长时间锁表
- 📝 **详细日志**: 完整的操作日志记录
- 🐳 **容器化**: 基于Docker/Podman容器部署

## 新的同步逻辑

### 工作流程
1. 数据清理完成后，扫描当前表目录下所有未同步的文件（不以.syn结尾）
2. 批量上传到远程服务器的 `/share/data-backup/表名/` 目录
3. 上传成功后，给文件名添加 `.syn` 后缀标记
4. 每张表只管理自己表文件夹下的文件

### SCP配置
```python
SCP_CONFIG = {
    'enabled': True,                           # 是否启用远程备份
    'host': '************',                   # 远程主机IP
    'port': 22,                               # SSH端口
    'username': 'M088607',                    # 用户名
    'password': '20250520a',                  # 密码
    'remote_path': '/share/data-backup',      # 远程基础路径
    'timeout': 300,                           # 传输超时时间（秒）
    'retry_count': 3,                         # 重试次数
    'delete_local_after_upload': False        # 上传后是否删除本地文件
}
```

## 快速开始

### 1. 部署服务
```bash
./deploy.sh
```

### 2. 测试同步功能
```bash
# 进入容器
podman exec -it db-cleaner /bin/bash

# 测试SCP同步
python manual_cleanup.py test-scp
```

### 3. 管理命令
```bash
# 查看服务状态
podman ps --filter name=db-cleaner

# 查看日志
podman logs -f db-cleaner

# 手动清理所有表
podman exec db-cleaner python manual_cleanup.py all

```

## 清理逻辑

1. 检查表中是否有超过保留期限的数据
2. 按主键升序获取最老的数据（每批 cleanup_count 条）
3. 将数据备份为CSV文件
4. 删除对应的数据记录
5. **同步当前表目录下所有未同步的文件到远程服务器**
6. **上传成功的文件重命名添加 .syn 后缀**
7. 重复步骤2-6，直到达到总清理数量或无更多数据

## 同步逻辑（新功能）

- **按表管理**: 每张表只管理自己表文件夹下的文件
- **远程路径**: 文件上传到 `/share/data-backup/表名/` 目录
- **同步标记**: 成功上传的文件会添加 `.syn` 后缀
- **批量同步**: 每次删除完成后，同步所有未同步的文件
- **重试机制**: 上传失败会自动重试

## 手动操作

### 列出配置的表
```bash
python manual_cleanup.py list
```

### 清理操作
```bash
# 清理所有表
python manual_cleanup.py all

# 清理指定表
python manual_cleanup.py table_name
```


## 文件结构

```
data-cleaner/
├── config.py              # 配置文件（包含SCP配置）
├── db_cleaner.py          # 主程序（包含智能同步功能）
├── manual_cleanup.py      # 手动清理和同步脚本
├── deploy.sh             # 部署脚本
├── Dockerfile            # 容器构建文件（预装SSH工具）
├── requirements.txt      # Python依赖
└── README.md            # 说明文档

backups/                  # 备份目录
├── table1/              # 表1的备份文件
│   ├── table1_1-1000_20240101_120000.csv
│   └── table1_1001-2000_20240101_130000.csv.syn  # 已同步文件
└── table2/              # 表2的备份文件
    └── table2_1-1000_20240101_140000.csv
```

## 容器环境

容器已预装SCP传输所需工具：
- `openssh-client`: SSH客户端
- `sshpass`: 密码认证支持

## 手动测试

在容器内手动测试：
```bash
# 测试SSH连接
ssh M088607@************

# 测试SCP上传到指定表目录
sshpass -p '20250520a' scp -o StrictHostKeyChecking=no test_file M088607@************:/share/data-backup/table_name/
```

## 故障排除

### 同步相关问题
1. **文件未标记**: 检查上传是否成功，失败的文件不会添加.syn后缀
2. **重复上传**: 只有不以.syn结尾的文件才会被同步
3. **路径问题**: 确保远程服务器上存在对应的表目录

### SCP相关问题
1. **网络连接**: 确保容器能访问远程服务器SSH端口
2. **认证失败**: 检查用户名和密码是否正确
3. **路径权限**: 确保远程目录存在且有写入权限
4. **防火墙**: 检查SSH端口(22)是否开放

### 日志查看
```bash
# 查看容器日志
podman logs db-cleaner

# 查看应用日志
podman exec db-cleaner ls -la /app/logs/

# 查看备份文件状态
podman exec db-cleaner find /app/backups -name "*.csv*" -type f
```

## 注意事项

1. **文件标记**: 成功上传的文件会自动添加.syn后缀，请勿手动修改
2. **远程路径**: 文件会上传到 `/share/data-backup/表名/` 目录结构
3. **批量同步**: 每次数据删除后会自动同步所有未同步文件
4. **网络要求**: 确保容器能够访问远程服务器的SSH端口
5. **监控日志**: 定期检查日志文件，确保清理和同步任务正常执行
