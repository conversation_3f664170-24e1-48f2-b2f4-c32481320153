#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库定时清理脚本
功能：定时清理指定表的历史数据，清理前先备份为CSV文件
"""

import os
import csv
import logging
import pymysql
import schedule
import time
import subprocess
import shlex
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass


@dataclass
class TableConfig:
    """表清理配置"""
    table_name: str
    primary_key: str = 'id'
    cleanup_count: int = 100000  # 每次清理的记录数
    total_cleanup_count: int = 1000000  # 总共需要清理的记录数
    schedule_time: str = '02:00'  # 清理时间 HH:MM 格式
    time_field: str = 'created_at'  # 时间字段名
    min_keep_days: int = 360  # 保留最近N天的数据


class DatabaseCleaner:
    """数据库清理器"""
    
    def __init__(self, db_config: Dict, backup_base_path: str = './backups', scp_config: Dict = None):
        """
        初始化数据库清理器

        Args:
            db_config: 数据库连接配置
            backup_base_path: 备份文件基础路径
            scp_config: SCP传输配置
        """
        self.db_config = db_config
        self.backup_base_path = backup_base_path
        self.scp_config = scp_config or {}
        self.logger = self._setup_logger()

        # 确保备份目录存在
        os.makedirs(backup_base_path, exist_ok=True)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('db_cleaner')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = './logs'
        os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            f'{log_dir}/db_cleaner_{datetime.now().strftime("%Y%m")}.log',
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _get_connection(self) -> pymysql.Connection:
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def _check_time_condition(self, table_name: str, primary_key: str,
                             cleanup_count: int, time_field: str,
                             min_keep_days: int) -> Tuple[bool, List[Dict], Optional[int], Optional[int]]:
        """
        检查时间条件，判断是否可以清理
        优化：从_get_cleanup_data获取的第一条数据中获取时间字段来判断，减少数据库查询

        Args:
            table_name: 表名
            primary_key: 主键字段名
            cleanup_count: 清理数量
            time_field: 时间字段名
            min_keep_days: 保留最近N天的数据

        Returns:
            (是否可以清理, 数据列表, 起始ID, 结束ID)
        """
        from datetime import datetime, timedelta

        # 先获取一批数据
        data, start_id, end_id = self._get_cleanup_data(
            table_name, primary_key, cleanup_count, time_field, min_keep_days
        )

        if not data:
            self.logger.info(f"表 {table_name} 没有数据需要清理")
            return False, [], None, None

        # 从第一条数据中获取时间字段值（最小的id对应的时间）
        first_record = data[0]
        if time_field not in first_record or not first_record[time_field]:
            self.logger.info(f"表 {table_name} 第一条记录的时间字段 {time_field} 为空")
            return False, [], None, None

        min_time = first_record[time_field]

        # 在代码中计算保留期限时间
        cutoff_time = datetime.now() - timedelta(days=min_keep_days)

        # 在代码中比较时间
        if min_time >= cutoff_time:
            self.logger.info(f"表 {table_name} 最早数据时间 {min_time} 未超过保留期限 {cutoff_time}，跳过清理")
            return False, [], None, None

        self.logger.info(f"表 {table_name} 最早数据时间: {min_time}, 保留期限: {cutoff_time}, 可以清理")
        return True, data, start_id, end_id

    def _get_cleanup_data(self, table_name: str, primary_key: str,
                         cleanup_count: int, time_field: str,
                         min_keep_days: int) -> Tuple[List[Dict], Optional[int], Optional[int]]:
        """
        获取需要清理的数据（从最老的数据开始）
        对于t_biz_kv8000_data表使用特殊的处理逻辑

        Args:
            table_name: 表名
            primary_key: 主键字段名
            cleanup_count: 清理数量
            time_field: 时间字段名
            min_keep_days: 保留最近N天的数据

        Returns:
            (数据列表, 起始ID, 结束ID)
        """
        connection = self._get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 对t_biz_kv8000_data表使用特殊处理逻辑
                if table_name == 't_biz_kv8000_data':
                    return self._get_kv8000_cleanup_data(cursor, table_name, primary_key,
                                                       cleanup_count, time_field, min_keep_days)

                # 其他表使用原有逻辑
                sql = f"""
                SELECT * FROM `{table_name}`
                ORDER BY `{primary_key}` ASC
                LIMIT {cleanup_count}
                """

                self.logger.info(f"开始查询表 {table_name} 的待清理数据，数量: {cleanup_count}")
                cursor.execute(sql)
                data = cursor.fetchall()

                if not data:
                    self.logger.info(f"表 {table_name} 没有需要清理的数据")
                    return [], None, None

                # 获取ID范围（ASC排序，第一条是最小ID，最后一条是最大ID）
                min_id = data[0][primary_key]
                max_id = data[-1][primary_key]

                self.logger.info(f"表 {table_name} 获取到 {len(data)} 条待清理数据 (ID: {min_id}-{max_id})")
                return data, min_id, max_id

        finally:
            connection.close()

    def _get_kv8000_cleanup_data(self, cursor, table_name: str, primary_key: str,
                               cleanup_count: int, time_field: str,
                               min_keep_days: int) -> Tuple[List[Dict], Optional[int], Optional[int]]:
        """
        专门处理t_biz_kv8000_data表的清理数据获取逻辑
        由于数据量大，使用特殊的查询和过滤策略

        Args:
            cursor: 数据库游标
            table_name: 表名
            primary_key: 主键字段名
            cleanup_count: 清理数量
            time_field: 时间字段名
            min_keep_days: 保留最近N天的数据

        Returns:
            (数据列表, 起始ID, 结束ID)
        """
        from datetime import datetime, timedelta

        self.logger.info(f"使用特殊逻辑处理表 {table_name}...")

        # 1. 查询最小ID
        self.logger.info("步骤1: 查询最小ID...")
        cursor.execute(f"SELECT {primary_key} FROM `{table_name}` ORDER BY {primary_key} ASC LIMIT 1")
        min_id_result = cursor.fetchone()

        if not min_id_result:
            self.logger.info(f"表 {table_name} 没有数据")
            return [], None, None

        min_id = min_id_result[primary_key]
        self.logger.info(f"最小ID: {min_id}")

        # 2. 查出对应数量的数据（从最小ID开始，查询更多数据以便过滤）
        batch_size = cleanup_count # 查询1倍数量，确保有足够数据过滤
        self.logger.info(f"步骤2: 从ID {min_id} 开始查询 {batch_size} 条数据...")

        sql = f"""
        SELECT * FROM `{table_name}`
        WHERE {primary_key} >= {min_id}
        LIMIT {batch_size}
        """

        cursor.execute(sql)
        raw_data = cursor.fetchall()

        if not raw_data:
            self.logger.info(f"表 {table_name} 没有可查询的数据")
            return [], None, None

        self.logger.info(f"查询到 {len(raw_data)} 条原始数据")

        # 3. 在代码中对read_time排序，过滤出需要删除的数据
        self.logger.info("步骤3: 按read_time排序并过滤需要删除的数据...")

        # 计算截止时间
        cutoff_time = datetime.now() - timedelta(days=min_keep_days)
        self.logger.info(f"删除截止时间: {cutoff_time}")

        # 过滤出需要删除的数据（read_time小于截止时间）
        filtered_data = []
        for row in raw_data:
            if row.get(time_field) and row[time_field] < cutoff_time:
                filtered_data.append(row)

        # 按read_time排序（从最老的开始）
        filtered_data.sort(key=lambda x: x[time_field] if x.get(time_field) else datetime.min)

        # 取前cleanup_count条数据
        data = filtered_data[:cleanup_count]

        if not data:
            self.logger.info(f"表 {table_name} 没有满足删除条件的数据")
            return [], None, None

        # 获取ID范围
        actual_min_id = data[0][primary_key]
        actual_max_id = data[-1][primary_key]

        self.logger.info(f"表 {table_name} 过滤后获取到 {len(data)} 条待清理数据")
        self.logger.info(f"ID范围: {actual_min_id} - {actual_max_id}")
        self.logger.info(f"时间范围: {data[0][time_field]} - {data[-1][time_field]}")

        return data, actual_min_id, actual_max_id

    def _backup_to_csv(self, table_name: str, data: List[Dict],
                      start_id: int, end_id: int) -> str:
        """
        将数据备份为CSV文件
        
        Args:
            table_name: 表名
            data: 要备份的数据
            start_id: 起始ID
            end_id: 结束ID
            
        Returns:
            备份文件路径
        """
        # 创建表对应的备份目录
        table_backup_dir = os.path.join(self.backup_base_path, table_name)
        os.makedirs(table_backup_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{table_name}_{start_id}-{end_id}_{timestamp}.csv"
        filepath = os.path.join(table_backup_dir, filename)
        
        # 写入CSV文件
        if data:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
        
        self.logger.info(f"数据已备份到: {filepath}")
        return filepath

    def _get_unsynced_files(self, table_name: str) -> List[str]:
        """
        获取指定表目录下未同步的文件列表

        Args:
            table_name: 表名

        Returns:
            未同步的文件路径列表
        """
        table_backup_dir = os.path.join(self.backup_base_path, table_name)
        if not os.path.exists(table_backup_dir):
            return []

        unsynced_files = []
        try:
            for filename in os.listdir(table_backup_dir):
                file_path = os.path.join(table_backup_dir, filename)
                # 只处理CSV文件且不是已同步的文件（不以.syn结尾）
                if (os.path.isfile(file_path) and
                    filename.endswith('.csv') and
                    not filename.endswith('.syn')):
                    unsynced_files.append(file_path)
        except Exception as e:
            self.logger.error(f"读取表备份目录失败 {table_backup_dir}: {e}")

        return unsynced_files

    def _upload_file_to_remote(self, local_file_path: str, table_name: str) -> bool:
        """
        使用SCP将单个备份文件上传到远程服务器

        Args:
            local_file_path: 本地文件路径
            table_name: 表名

        Returns:
            是否上传成功
        """
        if not self.scp_config.get('enabled', False):
            return True

        if not os.path.exists(local_file_path):
            self.logger.error(f"本地文件不存在: {local_file_path}")
            return False

        host = self.scp_config.get('host')
        port = self.scp_config.get('port', 22)
        username = self.scp_config.get('username')
        password = self.scp_config.get('password')
        remote_base_path = self.scp_config.get('remote_path', '/share/data-backup')
        timeout = self.scp_config.get('timeout', 300)
        retry_count = self.scp_config.get('retry_count', 3)

        if not all([host, username, password]):
            self.logger.error("SCP配置不完整，缺少必要参数")
            return False

        # 获取文件名
        filename = os.path.basename(local_file_path)

        # 构建远程表目录路径
        remote_table_path = f"{remote_base_path}/{table_name}".replace('\\', '/')
        remote_file_path = f"{remote_table_path}/{filename}".replace('\\', '/')

        # 构建SCP命令
        scp_command = [
            'sshpass', '-p', password,
            'scp',
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'UserKnownHostsFile=/dev/null',
            '-o', f'ConnectTimeout={timeout}',
            '-P', str(port),
            local_file_path,
            f"{username}@{host}:{remote_file_path}"
        ]

        # 重试上传
        for attempt in range(1, retry_count + 1):
            try:
                self.logger.info(f"开始上传文件到远程服务器 (尝试 {attempt}/{retry_count}): {filename} -> {remote_file_path}")

                # 执行SCP命令
                result = subprocess.run(
                    scp_command,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )

                if result.returncode == 0:
                    self.logger.info(f"文件上传成功: {filename} -> {host}:{remote_file_path}")
                    return True
                else:
                    error_msg = result.stderr.strip() if result.stderr else "未知错误"
                    self.logger.warning(f"文件上传失败 (尝试 {attempt}/{retry_count}): {error_msg}")

                    if attempt < retry_count:
                        self.logger.info(f"等待5秒后重试...")
                        time.sleep(5)

            except subprocess.TimeoutExpired:
                self.logger.warning(f"文件上传超时 (尝试 {attempt}/{retry_count})")
                if attempt < retry_count:
                    self.logger.info(f"等待5秒后重试...")
                    time.sleep(5)
            except Exception as e:
                self.logger.warning(f"文件上传异常 (尝试 {attempt}/{retry_count}): {e}")
                if attempt < retry_count:
                    self.logger.info(f"等待5秒后重试...")
                    time.sleep(5)

        self.logger.error(f"文件上传失败，已重试 {retry_count} 次: {filename}")
        return False

    def _mark_file_as_synced(self, file_path: str) -> bool:
        """
        将文件标记为已同步（重命名添加.syn后缀）

        Args:
            file_path: 文件路径

        Returns:
            是否标记成功
        """
        try:
            synced_file_path = file_path + '.syn'
            os.rename(file_path, synced_file_path)
            self.logger.info(f"文件已标记为已同步: {os.path.basename(synced_file_path)}")
            return True
        except Exception as e:
            self.logger.error(f"标记文件为已同步失败: {e}")
            return False

    def _sync_table_backups(self, table_name: str) -> bool:
        """
        同步指定表的所有未同步备份文件

        Args:
            table_name: 表名

        Returns:
            是否全部同步成功
        """
        if not self.scp_config.get('enabled', False):
            self.logger.info("SCP远程备份未启用，跳过同步")
            return True

        # 获取未同步的文件
        unsynced_files = self._get_unsynced_files(table_name)

        if not unsynced_files:
            self.logger.info(f"表 {table_name} 没有需要同步的文件")
            return True

        self.logger.info(f"表 {table_name} 发现 {len(unsynced_files)} 个未同步文件，开始同步...")

        success_count = 0
        for file_path in unsynced_files:
            filename = os.path.basename(file_path)
            self.logger.info(f"正在同步文件: {filename}")

            # 上传文件
            if self._upload_file_to_remote(file_path, table_name):
                # 上传成功，标记为已同步
                if self._mark_file_as_synced(file_path):
                    success_count += 1
                    self.logger.info(f"文件同步完成: {filename}")
                else:
                    self.logger.error(f"文件上传成功但标记失败: {filename}")
            else:
                self.logger.error(f"文件同步失败: {filename}")

        self.logger.info(f"表 {table_name} 同步完成: 成功 {success_count}/{len(unsynced_files)} 个文件")
        return success_count == len(unsynced_files)
    
    def _delete_data(self, table_name: str, primary_key: str,
                    start_id: int, end_id: int, data: List[Dict] = None,
                    min_keep_days: int = None) -> int:
        """
        删除指定范围的数据
        对于t_biz_kv8000_data表，使用具体的ID列表删除

        Args:
            table_name: 表名
            primary_key: 主键字段名
            start_id: 起始ID
            end_id: 结束ID
            data: 数据列表（用于t_biz_kv8000_data表的精确删除）

        Returns:
            删除的记录数
        """
        connection = self._get_connection()
        try:
            with connection.cursor() as cursor:
                # 对t_biz_kv8000_data表使用BETWEEN删除，但需要验证范围内数据的完整性
                if table_name == 't_biz_kv8000_data' and data:
                    if not data:
                        self.logger.info(f"表 {table_name} 没有需要删除的数据")
                        return 0

                    # 获取过滤后数据的ID范围
                    filtered_min_id = min(row[primary_key] for row in data)
                    filtered_max_id = max(row[primary_key] for row in data)

                    # 检查BETWEEN范围内是否有不满足删除条件的数据
                    check_sql = f"""
                    SELECT COUNT(*) as total_count,
                           SUM(CASE WHEN read_time >= DATE_SUB(NOW(), INTERVAL %s DAY) THEN 1 ELSE 0 END) as keep_count
                    FROM `{table_name}`
                    WHERE `{primary_key}` BETWEEN %s AND %s
                    """

                    # 使用传入的min_keep_days参数
                    if min_keep_days is None:
                        min_keep_days = 180  # 默认值

                    cursor.execute(check_sql, (min_keep_days, filtered_min_id, filtered_max_id))
                    result = cursor.fetchone()
                    total_count = result['total_count']
                    keep_count = result['keep_count']

                    if keep_count > 0:
                        self.logger.warning(f"表 {table_name} BETWEEN {filtered_min_id} AND {filtered_max_id} 范围内有 {keep_count}/{total_count} 条数据需要保留，跳过此次删除")
                        return 0

                    # 范围内所有数据都可以删除，执行BETWEEN删除
                    sql = f"DELETE FROM `{table_name}` WHERE `{primary_key}` BETWEEN %s AND %s"
                    affected_rows = cursor.execute(sql, (filtered_min_id, filtered_max_id))

                    connection.commit()
                    self.logger.info(f"从表 {table_name} 删除了 {affected_rows} 条记录 (ID: {filtered_min_id}-{filtered_max_id})")
                    return affected_rows

                else:
                    # 其他表使用原有的范围删除
                    sql = f"""
                    DELETE FROM `{table_name}`
                    WHERE `{primary_key}` BETWEEN %s AND %s
                    """
                    affected_rows = cursor.execute(sql, (start_id, end_id))
                    connection.commit()

                    self.logger.info(f"从表 {table_name} 删除了 {affected_rows} 条记录 (ID: {start_id}-{end_id})")
                    return affected_rows

        except Exception as e:
            connection.rollback()
            self.logger.error(f"删除数据失败: {e}")
            raise
        finally:
            connection.close()
    
    def clean_table(self, config: TableConfig) -> bool:
        """
        清理单个表的数据

        Args:
            config: 表清理配置

        Returns:
            是否成功
        """
        start_time = time.time()
        total_deleted = 0
        total_batches = 0

        try:
            self.logger.info(f"开始清理表: {config.table_name}")

            # 计算需要清理的批次数
            max_batches = (config.total_cleanup_count + config.cleanup_count - 1) // config.cleanup_count
            self.logger.info(f"表 {config.table_name} 计划清理 {config.total_cleanup_count} 条记录，"
                           f"每批 {config.cleanup_count} 条，共 {max_batches} 批")

            # 分批清理
            for batch_num in range(1, max_batches + 1):
                batch_start_time = time.time()

                self.logger.info(f"表 {config.table_name} 开始第 {batch_num}/{max_batches} 批清理")

                # 检查时间条件并获取需要清理的数据
                can_cleanup, data, start_id, end_id = self._check_time_condition(
                    config.table_name,
                    config.primary_key,
                    config.cleanup_count,
                    config.time_field,
                    config.min_keep_days
                )

                if not can_cleanup:
                    self.logger.info(f"表 {config.table_name} 第 {batch_num} 批不满足清理条件，结束清理")
                    break

                if not data:
                    self.logger.info(f"表 {config.table_name} 第 {batch_num} 批无数据需要清理，结束清理")
                    break

                # 备份数据
                backup_file = self._backup_to_csv(config.table_name, data, start_id, end_id)

                # 删除数据
                deleted_count = self._delete_data(
                    config.table_name,
                    config.primary_key,
                    start_id,
                    end_id,
                    data,  # 传递数据列表，用于t_biz_kv8000_data表的精确删除
                    config.min_keep_days  # 传递保留天数参数
                )

                # 删除完成后，同步当前表的所有未同步文件
                sync_success = self._sync_table_backups(config.table_name)
                if not sync_success:
                    self.logger.warning(f"表 {config.table_name} 部分文件同步失败，请检查日志")

                total_deleted += deleted_count
                total_batches += 1

                batch_duration = time.time() - batch_start_time
                self.logger.info(f"表 {config.table_name} 第 {batch_num} 批清理完成，"
                               f"删除 {deleted_count} 条记录，耗时 {batch_duration:.2f} 秒，"
                               f"备份文件: {backup_file}")

                # 如果已达到总清理数量，停止清理
                if total_deleted >= config.total_cleanup_count:
                    self.logger.info(f"表 {config.table_name} 已达到总清理数量限制 {config.total_cleanup_count}")
                    break

            total_duration = time.time() - start_time
            self.logger.info(f"表 {config.table_name} 清理完成！"
                           f"共清理 {total_batches} 批，删除 {total_deleted} 条记录，"
                           f"总耗时 {total_duration:.2f} 秒")
            return True

        except Exception as e:
            total_duration = time.time() - start_time
            self.logger.error(f"清理表 {config.table_name} 失败: {e}，"
                            f"已清理 {total_batches} 批，删除 {total_deleted} 条记录，"
                            f"耗时 {total_duration:.2f} 秒")
            return False


def main():
    """主函数"""
    # 从配置文件导入配置
    from config import DB_CONFIG, BACKUP_PATH, TABLE_CONFIGS, SCP_CONFIG

    # 创建清理器
    cleaner = DatabaseCleaner(DB_CONFIG, BACKUP_PATH, SCP_CONFIG)
    
    # 定义清理任务
    def cleanup_job(config: TableConfig):
        """清理任务"""
        cleaner.logger.info(f"执行定时清理任务: {config.table_name}")
        success = cleaner.clean_table(config)
        if success:
            cleaner.logger.info(f"定时清理任务完成: {config.table_name}")
        else:
            cleaner.logger.error(f"定时清理任务失败: {config.table_name}")
    
    # 注册定时任务
    for config in TABLE_CONFIGS:
        schedule.every().day.at(config.schedule_time).do(cleanup_job, config)
        cleaner.logger.info(f"已注册定时任务: {config.table_name} 在 {config.schedule_time} 执行")
    
    cleaner.logger.info("数据库清理服务已启动")
    
    # 运行定时任务
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        cleaner.logger.info("服务已停止")


if __name__ == '__main__':
    main()
